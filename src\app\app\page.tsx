'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FiBook, FiMessageSquare, FiLayers, FiFileText, FiUpload, FiChevronRight, FiCheck, FiList, FiCheckSquare, FiLogOut, FiRefreshCw, FiSettings, FiCalendar, FiDownload, FiPrinter, FiBarChart } from 'react-icons/fi';
import DocumentSelector, { DocumentSelectorRef } from '../../features/documents/components/DocumentSelector';
import QuestionForm from '../../features/conversations/components/QuestionForm';
import DocumentUploader from '../../features/documents/components/DocumentUploader';
import MindMapGenerator from '../../features/mindmaps/components/MindMapGenerator';
import FlashcardGenerator from '../../features/flashcards/components/FlashcardGenerator';
import SummaryGenerator from '../../features/summaries/components/SummaryGenerator';
import SummaryList from '../../features/summaries/components/SummaryList';
import DocumentManager from '../../features/documents/components/DocumentManager';
import FlashcardViewer from '../../features/flashcards/components/FlashcardViewer';
import TestGenerator from '../../features/tests/components/TestGenerator';
import TestViewer from '../../features/tests/components/TestViewer';
import Dashboard from '../../features/dashboard/components/Dashboard';
import TemarioManager from '../../features/temario/components/TemarioManager';

import SidebarMenu, { TabType } from '../../features/shared/components/SidebarMenu';
import { Documento } from '../../lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { useBackgroundGeneration } from '@/hooks/useBackgroundGeneration';
import { usePlanEstudiosResults } from '@/hooks/usePlanEstudiosResults';
import { obtenerTemarioUsuario } from '@/features/temario/services/temarioService';
import { tienePlanificacionConfigurada } from '@/features/planificacion/services/planificacionService';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';
import { obtenerPlanEstudiosActivoCliente } from '@/features/planificacion/services/planEstudiosClientService';
import PlanEstudiosViewer from '@/features/planificacion/components/PlanEstudiosViewer';
import { toast } from 'react-hot-toast';
import SessionInfo from '@/components/ui/SessionInfo';
import TokenStatsModal from '@/components/ui/TokenStatsModal';

export default function AppPage() {
  const [documentosSeleccionados, setDocumentosSeleccionados] = useState<Documento[]>([]);
  const [mostrarUploader, setMostrarUploader] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [isRefreshingDocuments, setIsRefreshingDocuments] = useState(false);
  const [planEstudios, setPlanEstudios] = useState<PlanEstudiosEstructurado | null>(null);
  const [temarioId, setTemarioId] = useState<string | null>(null);
  const [tienePlanificacion, setTienePlanificacion] = useState<boolean>(false);
  const [showTokenStats, setShowTokenStats] = useState(false);
  const [refreshSummaries, setRefreshSummaries] = useState(0);
  const { cerrarSesion, user, isLoading } = useAuth();
  const router = useRouter();
  const documentSelectorRef = useRef<DocumentSelectorRef>(null);

  // Hooks para el sistema de tareas en segundo plano
  const { generatePlanEstudios, isGenerating } = useBackgroundGeneration();

  // Hook para manejar los resultados del plan de estudios
  const { latestResult, isLoading: isPlanLoading } = usePlanEstudiosResults({
    onResult: (result) => {
      setPlanEstudios(result);
      toast.success('¡Plan de estudios generado exitosamente!');
    },
    onError: (error) => {
      toast.error(`Error al generar plan: ${error}`);
    }
  });

  // Verificar autenticación y redirigir si no está autenticado
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  // Cargar datos del temario y verificar planificación
  useEffect(() => {
    const cargarDatosTemario = async () => {
      if (!user) return;

      try {
        const temario = await obtenerTemarioUsuario();
        if (temario) {
          setTemarioId(temario.id);
          const tienePlan = await tienePlanificacionConfigurada(temario.id);
          setTienePlanificacion(tienePlan);

          // Verificar si ya existe un plan de estudios guardado
          const planExistente = await obtenerPlanEstudiosActivoCliente(temario.id);
          if (planExistente && planExistente.plan_data) {
            setPlanEstudios(planExistente.plan_data as PlanEstudiosEstructurado);
          } else {
            setPlanEstudios(null); // Limpiar plan si no existe
          }
        } else {
          // No hay temario, limpiar todo el estado
          setTemarioId(null);
          setTienePlanificacion(false);
          setPlanEstudios(null);
        }
      } catch (error) {
        console.error('Error al cargar datos del temario:', error);
        // En caso de error, limpiar el estado
        setTemarioId(null);
        setTienePlanificacion(false);
        setPlanEstudios(null);
      }
    };

    cargarDatosTemario();
  }, [user]);

  // Actualizar el plan cuando se reciba un resultado
  useEffect(() => {
    if (latestResult) {
      setPlanEstudios(latestResult);
    }
  }, [latestResult]);

  // Si está cargando o no hay usuario, mostrar pantalla de carga
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  const handleUploadSuccess = async () => {
    setShowUploadSuccess(true);
    setIsRefreshingDocuments(true);

    // Recargar la lista de documentos automáticamente
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos:', error);
    } finally {
      setIsRefreshingDocuments(false);
    }

    // Ocultar el mensaje después de 5 segundos
    setTimeout(() => setShowUploadSuccess(false), 5000);
  };

  const handleDocumentDeleted = async () => {
    // Recargar la lista de documentos cuando se elimina uno
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos después de eliminar:', error);
    }
  };

  const handleLogout = async () => {
    await cerrarSesion();
  };

  const handleGenerarPlanEstudios = async () => {
    if (!temarioId) {
      toast.error('No se encontró un temario configurado');
      return;
    }

    if (!tienePlanificacion) {
      toast.error('Necesitas completar la configuración de planificación en "Mi Temario"');
      return;
    }

    try {
      await generatePlanEstudios({
        temarioId,
        onComplete: (result) => {
          setPlanEstudios(result);
        },
        onError: (error) => {
          if (error.includes('planificación configurada')) {
            toast.error('Necesitas completar la configuración de planificación en "Mi Temario"');
          } else {
            toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');
          }
        }
      });
    } catch (error) {
      console.error('Error al iniciar generación del plan:', error);
    }
  };

  const handleDescargarPlan = () => {
    if (!planEstudios) return;

    // Convertir el plan estructurado a texto
    const planTexto = convertirPlanATexto(planEstudios);
    const blob = new Blob([planTexto], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `plan-estudios-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Plan descargado exitosamente');
  };

  const convertirPlanATexto = (plan: PlanEstudiosEstructurado): string => {
    let texto = `# Plan de Estudios Personalizado\n\n`;
    texto += `${plan.introduccion}\n\n`;
    texto += `## Resumen del Plan\n\n`;
    texto += `- **Tiempo total de estudio:** ${plan.resumen.tiempoTotalEstudio}\n`;
    texto += `- **Número de temas:** ${plan.resumen.numeroTemas}\n`;
    texto += `- **Duración estudio nuevo:** ${plan.resumen.duracionEstudioNuevo}\n`;
    texto += `- **Duración repaso final:** ${plan.resumen.duracionRepasoFinal}\n\n`;

    texto += `## Cronograma Semanal\n\n`;
    plan.semanas.forEach(semana => {
      texto += `### Semana ${semana.numero} (${semana.fechaInicio} - ${semana.fechaFin})\n\n`;
      texto += `**Objetivo:** ${semana.objetivoPrincipal}\n\n`;
      semana.dias.forEach(dia => {
        texto += `**${dia.dia} (${dia.horas}h):**\n`;
        dia.tareas.forEach(tarea => {
          texto += `- ${tarea.titulo} (${tarea.duracionEstimada})\n`;
          if (tarea.descripcion) {
            texto += `  ${tarea.descripcion}\n`;
          }
        });
        texto += '\n';
      });
    });

    texto += `## Estrategia de Repasos\n\n${plan.estrategiaRepasos}\n\n`;
    texto += `## Próximos Pasos\n\n${plan.proximosPasos}\n`;

    return texto;
  };

  const handleImprimirPlan = () => {
    if (!planEstudios) return;

    const planTexto = convertirPlanATexto(planEstudios);
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Plan de Estudios Personalizado</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
              h1, h2, h3 { color: #333; }
              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }
              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }
              ul, ol { margin-left: 20px; }
              strong { color: #2563eb; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            <div id="content"></div>
            <script>
              // Convertir markdown a HTML básico para impresión
              const markdown = ${JSON.stringify(planTexto)};
              const content = markdown
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')
                .replace(/\\n/g, '<br>');
              document.getElementById('content').innerHTML = content;
              window.print();
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // Función para convertir TabType a string para el Dashboard
  const handleNavigateToTab = (tab: string) => {
    setActiveTab(tab as TabType);
  };

  // Handler para cuando se genera un resumen
  const handleSummaryGenerated = (summaryId: string) => {
    setRefreshSummaries(prev => prev + 1);
    toast.success('Resumen generado exitosamente');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                OposiAI
              </h1>
              <p className="text-sm text-gray-500">Tu asistente inteligente para oposiciones</p>
              <SessionInfo className="mt-1" />
            </div>
            <div className="flex items-center space-x-4">
              {user && (
                <div className="text-sm text-gray-600">
                  Hola, {user.email?.split('@')[0]}
                </div>
              )}
              <button
                onClick={() => setShowTokenStats(true)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="Ver estadísticas de uso de IA"
              >
                <FiBarChart className="w-4 h-4" />
              </button>
              <button
                onClick={() => setMostrarUploader(!mostrarUploader)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <FiUpload className="mr-2" />
                {mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'}
              </button>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiLogOut className="mr-2" />
                Cerrar sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="px-4 sm:px-6 lg:px-8 py-8">
        {/* Uploader */}
        {mostrarUploader && (
          <div className="mb-8 transition-all duration-300 ease-in-out">
            <DocumentUploader onSuccess={handleUploadSuccess} />
          </div>
        )}

        {showUploadSuccess && (
          <div className="mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200">
            <div className="flex items-center">
              <FiCheck className="text-green-500 mr-2 flex-shrink-0" />
              <div>
                <p className="font-medium">¡Documento subido exitosamente!</p>
                <p className="text-sm text-green-700 mt-1">
                  {isRefreshingDocuments ? (
                    <span className="flex items-center">
                      <FiRefreshCw className="animate-spin mr-1" />
                      Actualizando lista de documentos...
                    </span>
                  ) : (
                    "El documento ya está disponible en los desplegables de selección."
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Sección de Documentos Seleccionados */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center mb-3">
            <FiFileText className="w-5 h-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Documentos Seleccionados</h2>
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Selecciona los documentos que quieres usar para generar contenido con IA.
          </p>
          <DocumentSelector
            ref={documentSelectorRef}
            onSelectionChange={setDocumentosSeleccionados}
          />
          {documentosSeleccionados.length > 0 && (
            <div className="mt-3 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800 font-medium">
                <strong>{documentosSeleccionados.length}</strong> documento{documentosSeleccionados.length !== 1 ? 's' : ''} seleccionado{documentosSeleccionados.length !== 1 ? 's' : ''}.
              </p>
              <div className="mt-2 flex flex-wrap gap-1">
                {documentosSeleccionados.map((doc) => (
                  <span
                    key={doc.id}
                    className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {doc.numero_tema && `Tema ${doc.numero_tema}: `}{doc.titulo}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Layout expandido para todas las pestañas - ancho completo */}
        <div className="flex gap-6 mb-8">
          {/* Sidebar con menú jerárquico */}
          <SidebarMenu
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Área de contenido expandida */}
          <div className="flex-1">
            {activeTab === 'dashboard' ? (
              <Dashboard onNavigateToTab={handleNavigateToTab} />
            ) : (
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="p-6">
                  {activeTab === 'temario' && <TemarioManager />}

                  {activeTab === 'planEstudios' && (
                    <div>
                      {/* Header */}
                      <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">Mi Plan de Estudios</h2>
                        <div className="flex gap-2">
                          {planEstudios && (
                            <>
                              <button
                                onClick={handleGenerarPlanEstudios}
                                disabled={isPlanLoading || isGenerating('plan-estudios')}
                                className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              >
                                <FiRefreshCw className={`w-4 h-4 ${(isPlanLoading || isGenerating('plan-estudios')) ? 'animate-spin' : ''}`} />
                                Regenerar Plan
                              </button>
                              <button
                                onClick={handleDescargarPlan}
                                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                              >
                                <FiDownload className="w-4 h-4" />
                                Descargar
                              </button>
                              <button
                                onClick={handleImprimirPlan}
                                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                              >
                                <FiPrinter className="w-4 h-4" />
                                Imprimir
                              </button>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Contenido */}
                      {isPlanLoading || isGenerating('plan-estudios') ? (
                        <div className="text-center py-12">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Generando tu plan personalizado</h3>
                          <p className="text-gray-600">La IA está analizando tu temario y configuración...</p>
                        </div>
                      ) : planEstudios && temarioId ? (
                        <PlanEstudiosViewer
                          plan={planEstudios}
                          temarioId={temarioId}
                        />
                      ) : (
                        <div className="text-center py-12">
                          <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <FiCalendar className="w-10 h-10 text-teal-600" />
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">
                            Genera tu Plan de Estudios Personalizado
                          </h3>
                          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                            Crea un plan de estudios personalizado basado en tu temario y configuración de planificación
                          </p>
                          <button
                            onClick={handleGenerarPlanEstudios}
                            disabled={!tienePlanificacion}
                            className="inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            <FiCalendar className="w-5 h-5 mr-3" />
                            Generar Plan de Estudios
                          </button>
                          {!tienePlanificacion && (
                            <p className="text-sm text-red-600 mt-4">
                              Necesitas completar la configuración de planificación en "Mi Temario"
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === 'preguntas' && (
                    <QuestionForm documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'mapas' && (
                    <MindMapGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'flashcards' && (
                    <FlashcardGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'tests' && (
                    <TestGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'misTests' && <TestViewer />}

                  {activeTab === 'misFlashcards' && <FlashcardViewer />}

                  {activeTab === 'resumenes' && (
                    <div className="space-y-6">
                      <SummaryGenerator
                        documentosSeleccionados={documentosSeleccionados}
                        onSummaryGenerated={handleSummaryGenerated}
                      />
                      <hr className="border-gray-200" />
                      <SummaryList refreshTrigger={refreshSummaries} />
                    </div>
                  )}

                  {activeTab === 'gestionar' && (
                    <DocumentManager onDocumentDeleted={handleDocumentDeleted} />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center">
              <span className="text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} OposiAI - Asistente para Oposiciones
              </span>
            </div>
            <div className="mt-4 md:mt-0">
              <nav className="flex space-x-6">
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Términos
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Privacidad
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Contacto
                </a>
              </nav>
            </div>
          </div>
        </div>
      </footer>



      {/* Modal de estadísticas de tokens */}
      <TokenStatsModal
        isOpen={showTokenStats}
        onClose={() => setShowTokenStats(false)}
      />
    </div>
  );
}
