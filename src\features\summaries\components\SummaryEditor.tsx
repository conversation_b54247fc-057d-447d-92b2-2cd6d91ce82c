'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { FiEdit3, FiX, FiLoader } from 'react-icons/fi';
import { Resumen, actualizarResumen } from '@/lib/supabase/resumenesService';
import { markdownToHTML } from '@/utils/markdownToHTML';

interface SummaryEditorProps {
  resumen: Resumen;
  onClose: () => void;
  onEdited: () => void;
}

export default function SummaryEditor({ resumen, onClose, onEdited }: SummaryEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [resumenEditado, setResumenEditado] = useState<string | null>(null);
  const [tiempoEstimado, setTiempoEstimado] = useState<number>(0);

  const estimarTiempoEdicion = (contenido: string): number => {
    // Estimar tiempo basado en la longitud del contenido
    const palabras = contenido.split(' ').length;
    const tiempoBase = Math.max(30, Math.min(120, palabras / 50)); // Entre 30 segundos y 2 minutos
    return Math.round(tiempoBase);
  };

  const handleEditarResumen = async () => {
    try {
      console.log('🔄 Iniciando edición de resumen...');
      setIsEditing(true);
      setTiempoEstimado(estimarTiempoEdicion(resumen.contenido));

      console.log('📄 Resumen a editar:', {
        id: resumen.id,
        titulo: resumen.titulo,
        contenidoLength: resumen.contenido?.length || 0
      });

      // Preparar datos para la API
      const requestData = {
        action: 'editarResumen',
        peticion: resumen.titulo,
        contextos: [resumen.contenido]
      };

      console.log('📡 Enviando petición de edición a la API:', {
        action: requestData.action,
        peticion: requestData.peticion,
        contextosLength: requestData.contextos.length,
        primerContextoLength: requestData.contextos[0]?.length || 0
      });

      // Editar el resumen usando la API
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Error en la respuesta de la API:', errorData);
        throw new Error(errorData.detalles || errorData.error || 'Error al editar el resumen');
      }

      const data = await response.json();
      console.log('✅ Respuesta de la API recibida:', {
        hasResult: !!data.result,
        resultType: typeof data.result,
        resultLength: data.result?.length || 0
      });

      if (!data.result) {
        console.error('❌ No se recibió resultado de la API');
        throw new Error('No se recibió el resumen editado de la API');
      }

      const resumenEditadoContent = data.result;

      if (typeof resumenEditadoContent !== 'string') {
        console.error('❌ El resultado no es una cadena:', typeof resumenEditadoContent);
        throw new Error('El formato del resumen editado no es válido');
      }

      console.log('✅ Contenido del resumen editado recibido, longitud:', resumenEditadoContent.length);
      setResumenEditado(resumenEditadoContent);

      toast.success('Resumen editado exitosamente');

    } catch (error: any) {
      console.error('❌ Error al editar resumen:', error);
      toast.error(error.message || 'Error al editar el resumen');
    } finally {
      setIsEditing(false);
    }
  };

  const handleAceptarEdicion = async () => {
    if (!resumenEditado) return;

    try {
      console.log('💾 Guardando resumen editado...');

      const success = await actualizarResumen(
        resumen.id,
        resumen.titulo,
        resumenEditado,
        resumen.instrucciones || undefined
      );

      if (success) {
        console.log('✅ Resumen actualizado exitosamente');
        toast.success('Resumen editado y guardado exitosamente');
        onEdited();
        onClose();
      } else {
        console.error('❌ Error al actualizar el resumen');
        toast.error('Error al guardar la edición del resumen');
      }
    } catch (error: any) {
      console.error('❌ Error al guardar edición:', error);
      toast.error(error.message || 'Error al guardar la edición');
    }
  };

  const handleRechazarEdicion = () => {
    setResumenEditado(null);
    toast.success('Edición descartada');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">Editor de Resumen</h2>
            <p className="text-sm text-gray-600 mt-1">{resumen.titulo}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {!resumenEditado ? (
            // Vista original
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-800">Resumen Original</h3>
                <button
                  onClick={handleEditarResumen}
                  disabled={isEditing}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isEditing ? (
                    <>
                      <FiLoader className="animate-spin mr-2" size={16} />
                      Editando...
                    </>
                  ) : (
                    <>
                      <FiEdit3 className="mr-2" size={16} />
                      Editar con IA
                    </>
                  )}
                </button>
              </div>

              {isEditing && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <FiLoader className="animate-spin mr-2 text-blue-600" size={16} />
                    <span className="text-blue-800 text-sm">
                      Editando resumen... Tiempo estimado: {tiempoEstimado} segundos
                    </span>
                  </div>
                  <p className="text-xs text-blue-600 mt-2">
                    La edición del resumen puede tardar unos minutos, si encuentra algún fallo una vez finalizado vuelve a editar, OposiAI puede cometer errores de configuración
                  </p>
                </div>
              )}

              <div 
                className="prose max-w-none bg-gray-50 p-4 rounded-lg"
                dangerouslySetInnerHTML={{ __html: markdownToHTML(resumen.contenido) }}
              />
            </div>
          ) : (
            // Vista comparativa
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Resumen Original */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Resumen Original</h3>
                <div 
                  className="prose max-w-none bg-gray-50 p-4 rounded-lg text-sm"
                  dangerouslySetInnerHTML={{ __html: markdownToHTML(resumen.contenido) }}
                />
              </div>

              {/* Resumen Editado */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-green-800">Resumen Editado</h3>
                <div 
                  className="prose max-w-none bg-green-50 p-4 rounded-lg text-sm border-2 border-green-200"
                  dangerouslySetInnerHTML={{ __html: markdownToHTML(resumenEditado) }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          {resumenEditado ? (
            <>
              <button
                onClick={handleRechazarEdicion}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Descartar Edición
              </button>
              <button
                onClick={handleAceptarEdicion}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Aceptar Edición
              </button>
            </>
          ) : (
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cerrar
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
