// Script de prueba para verificar el esquema de validación
const { z } = require('zod');

// Recrear los esquemas para probar
const EditarResumenSchema = z.object({
  action: z.literal('editarResumen'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)).length(1),
});

// Datos de prueba que envía el componente
const testData = {
  action: 'editarResumen',
  peticion: 'Resumen: Tema de prueba',
  contextos: ['Este es el contenido del resumen original que queremos editar...']
};

console.log('🧪 Probando validación del esquema EditarResumenSchema...');
console.log('📥 Datos de prueba:', JSON.stringify(testData, null, 2));

const result = EditarResumenSchema.safeParse(testData);

if (result.success) {
  console.log('✅ Validación exitosa!');
  console.log('📤 Datos validados:', result.data);
} else {
  console.log('❌ Error de validación:');
  console.log(result.error.errors);
}
