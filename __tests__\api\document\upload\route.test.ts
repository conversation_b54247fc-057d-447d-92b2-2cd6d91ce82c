import { POST } from '../route'; // Ajusta la ruta según tu estructura
import { NextRequest } from 'next/server';
import { guardarDocumento } from '@/lib/supabase/documentosService'; // Mocked
// Import pdfParse for mocking, but we'll also need the actual implementation for one test
import originalPdfParse from 'pdf-parse'; 

import * as fs from 'fs/promises';
import * as path from 'path';

// Mockear dependencias
jest.mock('@/lib/supabase/documentosService', () => ({
  guardarDocumento: jest.fn(),
}));

// Advanced mock for pdf-parse to allow passthrough for specific tests
let useActualPdfParse = false;
jest.mock('pdf-parse', () => ({
  __esModule: true, 
  default: jest.fn(async (buffer: Buffer) => {
    if (useActualPdfParse) {
      const actualPdfParse = jest.requireActual('pdf-parse');
      return actualPdfParse.default(buffer);
    }
    // Default mock behavior for other tests, ensure it matches what original pdf-parse returns
    return Promise.resolve({ 
      text: 'Texto extraído del PDF por mock', 
      numpages: 1, 
      numrender: 1, 
      info: null, 
      metadata: null, 
      version: '1.10.100' 
    });
  }),
}));

describe('/api/document/upload POST endpoint', () => {
  const mockGuardarDocumento = guardarDocumento as jest.Mock;
  // Get the mocked pdfParse function
  const mockPdfParse = originalPdfParse as unknown as jest.Mock;


  beforeEach(() => {
    // Limpiar mocks antes de cada prueba
    mockGuardarDocumento.mockReset();
    // Reset the mockPdfParse call history and any specific mock implementations
    mockPdfParse.mockReset();
    // Default mock implementation for most tests:
    mockPdfParse.mockResolvedValue({ 
      text: 'Texto extraído del PDF por mock para beforeEach',
      numpages: 1,
      numrender: 1,
      info: null,
      metadata: null,
      version: '1.10.100'
    });
    useActualPdfParse = false; // Reset flag before each test
  });

  // Helper para crear un mock de NextRequest con FormData
  const createMockRequest = (file?: File, contentType?: string): NextRequest => {
    const formData = new FormData();
    if (file) {
      formData.append('file', file);
    }
    
    const headers = new Headers();
    if (contentType !== undefined) { // Allow explicitly setting no content-type
        headers.append('Content-Type', contentType || 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW');
    }


    // Simular el objeto Request
    const mockReq = {
      headers,
      formData: async () => formData,
      // Añade otros métodos/propiedades de NextRequest que uses si es necesario
    } as unknown as NextRequest;
    
    return mockReq;
  };

  // Helper para crear un mock de File
  const createMockFile = (name: string, type: string, content: string): File => {
    const blob = new Blob([content], { type });
    return new File([blob], name, { type });
  };

  test('Subida Exitosa de PDF (con mock de extracción)', async () => {
    const mockFile = createMockFile('test.pdf', 'application/pdf', 'pdf content');
    const mockRequest = createMockRequest(mockFile);
    
    // Ensure pdf-parse is mocked for this test
    useActualPdfParse = false; 
    mockPdfParse.mockResolvedValueOnce({ text: 'Texto específico del mock para este PDF' });
    mockGuardarDocumento.mockResolvedValue('mock-document-id-pdf');

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(201);
    expect(body.message).toBe('Documento procesado y guardado con éxito.');
    expect(body.documentId).toBe('mock-document-id-pdf');
    expect(mockPdfParse).toHaveBeenCalledTimes(1); // Verifies that our mock was called
    expect(mockGuardarDocumento).toHaveBeenCalledWith({
      titulo: 'test.pdf',
      contenido: 'Texto específico del mock para este PDF', // Check against the mockResolvedValueOnce
      categoria: undefined,
      numero_tema: undefined,
      tipo_original: 'pdf',
    });
  });

  test('Procesamiento de PDF con Eliminación de Cabecera/Pie (usando PDF real)', async () => {
    useActualPdfParse = true; // Enable actual pdf-parse for this test

    const pdfPath = path.join(__dirname, 'sample-with-header-footer.pdf');
    const pdfBuffer = await fs.readFile(pdfPath);
    const mockFile = new File([pdfBuffer], 'sample-with-header-footer.pdf', { type: 'application/pdf' });
    const mockRequest = createMockRequest(mockFile);

    mockGuardarDocumento.mockResolvedValue('mock-document-id-real-pdf');

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(201);
    expect(body.documentId).toBe('mock-document-id-real-pdf');
    
    // pdf-parse (actual) should have been called via the mock's passthrough
    expect(mockPdfParse).toHaveBeenCalledTimes(1); 

    expect(mockGuardarDocumento).toHaveBeenCalledTimes(1);
    const AwaitedCall = mockGuardarDocumento.mock.calls[0][0];

    const headerContent = "Test Document Header Unique";
    const footerContent = "Page 1 Test Footer Unique";
    const mainContent = "This is the main unique content of the PDF example.";

    // Debugging: Log the extracted content if the test fails
    // console.log("Extracted content for PDF processing test:", AwaitedCall.contenido);

    expect(AwaitedCall.titulo).toBe('sample-with-header-footer.pdf');
    expect(AwaitedCall.contenido).not.toContain(headerContent);
    expect(AwaitedCall.contenido).not.toContain(footerContent);
    expect(AwaitedCall.contenido).toContain(mainContent);
    // Check if some part of the main content is there, accounting for potential line breaks
    expect(AwaitedCall.contenido.replace(/\s+/g, ' ')).toContain(mainContent.replace(/\s+/g, ' '));
    expect(AwaitedCall.tipo_original).toBe('pdf');
  });


  test('Subida Exitosa de TXT (sin regresión y sin invocar pdf-parse)', async () => {
    useActualPdfParse = false; // Ensure pdf-parse mock is standard for this test
    const mockFile = createMockFile('test.txt', 'text/plain', 'Texto del archivo TXT sin modificar');
    const mockRequest = createMockRequest(mockFile);

    mockGuardarDocumento.mockResolvedValue('mock-document-id-txt');

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(201);
    expect(body.message).toBe('Documento procesado y guardado con éxito.');
    expect(body.documentId).toBe('mock-document-id-txt');
    
    // Crucially, pdf-parse (neither actual nor mock) should be called for TXT files.
    // The mockPdfParse.mock.calls.length should be 0 if it was reset correctly.
    expect(mockPdfParse.mock.calls.length).toBe(0); 
    
    expect(mockGuardarDocumento).toHaveBeenCalledWith({
      titulo: 'test.txt',
      contenido: 'Texto del archivo TXT sin modificar',
      categoria: undefined,
      numero_tema: undefined,
      tipo_original: 'txt',
    });
  });

  test('Error de Extracción de PDF (PDF corrupto)', async () => {
    const mockFile = createMockFile('corrupt.pdf', 'application/pdf', 'pdf content');
    const mockRequest = createMockRequest(mockFile);

    mockPdfParse.mockRejectedValue(new Error('PDF parsing error'));

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(422);
    expect(body.error).toBe('Error al procesar el archivo PDF.');
    expect(body.details).toContain('PDF parsing error from mock');
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });

  test('Tipo de Archivo no Soportado (ej. imagen)', async () => {
    const mockFile = createMockFile('image.png', 'image/png', 'image content');
    const mockRequest = createMockRequest(mockFile);

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(415);
    expect(body.error).toBe('Tipo de archivo no soportado: image/png. Solo se permiten .txt y .pdf.');
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });

  test('No se Proporciona Archivo', async () => {
    const mockRequest = createMockRequest(); // Sin archivo

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(400);
    expect(body.error).toBe('No se proporcionó ningún archivo.');
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });
  
  test('Petición sin Content-Type multipart/form-data', async () => {
    const mockFile = createMockFile('test.txt', 'text/plain', 'file content');
    // Pass null to explicitly avoid setting Content-Type for this test case
    const mockRequest = createMockRequest(mockFile, null as any); 

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(415);
    expect(body.error).toBe("El cuerpo de la petición debe ser 'multipart/form-data'.");
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });


  test('Contenido Vacío Después de Extracción (PDF)', async () => {
    const mockFile = createMockFile('empty.pdf', 'application/pdf', 'pdf content');
    const mockRequest = createMockRequest(mockFile);

    mockPdfParse.mockResolvedValue({ text: '' }); // PDF parse devuelve texto vacío

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(422);
    expect(body.error).toBe('El contenido extraído del archivo está vacío.');
    expect(mockPdfParse).toHaveBeenCalledTimes(1);
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });

  test('Contenido Vacío Después de Extracción (TXT)', async () => {
    const mockFile = createMockFile('empty.txt', 'text/plain', ''); // Archivo TXT vacío
    const mockRequest = createMockRequest(mockFile);

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(422);
    expect(body.error).toBe('El contenido extraído del archivo está vacío.');
    expect(mockGuardarDocumento).not.toHaveBeenCalled();
  });
  
  test('Error al guardar el documento en la base de datos', async () => {
    const mockFile = createMockFile('test.txt', 'text/plain', 'Contenido válido');
    const mockRequest = createMockRequest(mockFile);

    mockGuardarDocumento.mockResolvedValue(null); // Simula fallo al guardar

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(500);
    expect(body.error).toBe('Error al guardar el documento en la base de datos.');
    expect(mockGuardarDocumento).toHaveBeenCalledWith({
      titulo: 'test.txt',
      contenido: 'Contenido válido',
      categoria: undefined,
      numero_tema: undefined,
      tipo_original: 'txt',
    });
  });

  test('Error inesperado durante el procesamiento', async () => {
    const mockFile = createMockFile('error.txt', 'text/plain', 'content');
    const mockRequest = createMockRequest(mockFile);

    mockGuardarDocumento.mockImplementation(() => {
      throw new Error("Error inesperado en guardarDocumento");
    });

    const response = await POST(mockRequest);
    const body = await response.json();

    expect(response.status).toBe(500);
    expect(body.error).toBe('Error interno del servidor.');
    expect(body.details).toBe('Error inesperado en guardarDocumento');
  });

});
