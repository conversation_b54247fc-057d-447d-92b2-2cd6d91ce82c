import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import Select from 'react-select';
import { Documento, obtenerDocumentos } from '@/lib/supabase';

interface DocumentSelectorProps {
  onSelectionChange: (documentos: Documento[]) => void;
}

export interface DocumentSelectorRef {
  recargarDocumentos: () => Promise<void>;
}

const DocumentSelector = forwardRef<DocumentSelectorRef, DocumentSelectorProps>(
  ({ onSelectionChange }, ref) => {
    const [documentos, setDocumentos] = useState<Documento[]>([]);
    const [selectedOptions, setSelectedOptions] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const cargarDocumentos = async () => {
      setIsLoading(true);
      try {
        const docs = await obtenerDocumentos();
        setDocumentos(docs);
      } catch (error) {
        console.error('Error al cargar documentos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    useEffect(() => {
      cargarDocumentos();
    }, []);

    // Exponer la función de recarga a través de ref
    useImperativeHandle(ref, () => ({
      recargarDocumentos: cargarDocumentos
    }));

    const handleChange = (selectedOptions: any) => {
      setSelectedOptions(selectedOptions || []);

      // Convertir las opciones seleccionadas en documentos completos
      const docsSeleccionados = selectedOptions.map((option: any) =>
        documentos.find(doc => doc.id === option.value)
      ).filter(Boolean) as Documento[];

      onSelectionChange(docsSeleccionados);
    };

    const options = documentos.map(doc => ({
      value: doc.id,
      label: `${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo} ${doc.categoria ? `(${doc.categoria})` : ''}`
    }));

    return (
      <div className="mb-6">
        <label className="block text-gray-700 text-sm font-bold mb-2">
          Selecciona los documentos para consultar:
        </label>
        <Select
          instanceId="document-selector"
          isMulti
          isLoading={isLoading}
          options={options}
          className="basic-multi-select"
          classNamePrefix="select"
          placeholder="Selecciona uno o más documentos..."
          noOptionsMessage={() => "No hay documentos disponibles"}
          onChange={handleChange}
          value={selectedOptions}
        />
        {selectedOptions.length === 0 && (
          <p className="text-red-500 text-xs italic mt-1">
            Debes seleccionar al menos un documento
          </p>
        )}
      </div>
    );
});

DocumentSelector.displayName = 'DocumentSelector';

export default DocumentSelector;
